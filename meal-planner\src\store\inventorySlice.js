import { createSlice } from "@reduxjs/toolkit";

const inventorySlice = createSlice({
  name: "inventory",
  initialState: {
    inventory: [
      {
        id: 1,
        ingredientName: "Spaghetti",
        quantity: 800,
        unit: "g",
        alertThreshold: 200,
        createdAt: (new Date()).toLocaleString()
      },
      {
        id: 2,
        ingredientName: "Viande hachée",
        quantity: 500,
        unit: "g",
        alertThreshold: 100,
        createdAt: (new Date()).toLocaleString()
      },
      {
        id: 3,
        ingredientName: "Tomates",
        quantity: 5,
        unit: "boîtes",
        alertThreshold: 1,
        createdAt: (new Date()).toLocaleString()
      },
      {
        id: 4,
        ingredientName: "Oignon",
        quantity: 3,
        unit: "pièces",
        alertThreshold: 1,
        createdAt: (new Date()).toLocaleString()
      },
      {
        id: 5,
        ingredientName: "Laitue",
        quantity: 2,
        unit: "têtes",
        alertThreshold: 1,
        createdAt: (new Date()).toLocaleString()
      }
    ],
    loading: false,
    error: null,
  },
  reducers: {
    addInventoryItem: (state, action) => {
      state.inventory.push({
        id: state.inventory.length + 1,
        ...action.payload,
        createdAt: (new Date()).toLocaleString(),
      });
    },
    updateInventoryItem: (state, action) => {
      const { id, ingredientName, quantity, unit, alertThreshold } = action.payload;
      const item = state.inventory.find(item => item.id === id);
      if (item) {
        item.ingredientName = ingredientName;
        item.quantity = quantity;
        item.unit = unit;
        item.alertThreshold = alertThreshold;
      }
    },
    updateInventoryQuantity: (state, action) => {
      const { ingredientName, quantityUsed } = action.payload;
      const item = state.inventory.find(item => 
        item.ingredientName.toLowerCase() === ingredientName.toLowerCase()
      );
      if (item) {
        item.quantity = Math.max(0, item.quantity - quantityUsed);
      }
    },
    deleteInventoryItem: (state, action) => {
      state.inventory = state.inventory.filter(item => item.id !== action.payload);
    },
    clearInventory: (state) => {
      state.inventory = [];
    },
  },
  selectors: {
    getInventory: (state) => state.inventory || [],
    getInventoryById: (state, id) => (state.inventory || []).find(item => item.id === id),
    getInventoryByName: (state, name) =>
      (state.inventory || []).find(item =>
        item.ingredientName.toLowerCase() === name.toLowerCase()
      ),
    getLowStockItems: (state) =>
      (state.inventory || []).filter(item => item.quantity <= item.alertThreshold),
  },
});

export const { 
  getInventory, 
  getInventoryById, 
  getInventoryByName, 
  getLowStockItems 
} = inventorySlice.selectors;

export const { 
  addInventoryItem, 
  updateInventoryItem, 
  updateInventoryQuantity,
  deleteInventoryItem, 
  clearInventory 
} = inventorySlice.actions;

export default inventorySlice.reducer;
