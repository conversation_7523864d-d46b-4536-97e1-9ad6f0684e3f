import React from 'react';
import { useSelector, useDispatch } from 'react-redux';
import { getMeals, deleteMeal } from '../store/mealsSlice';
import { getInventory } from '../store/inventorySlice';
import { checkInventoryAvailability, calculateAvailabilityPercentage } from '../utils/inventoryUtils';

export default function MealList({ onEditMeal }) {
  const dispatch = useDispatch();
  const meals = useSelector((state) => state.meals?.meals || []);
  const inventory = useSelector((state) => state.inventory?.inventory || []);

  const handleDelete = (id) => {
    if (window.confirm('Êtes-vous sûr de vouloir supprimer ce repas ?')) {
      dispatch(deleteMeal(id));
    }
  };

  const getCategoryColor = (category) => {
    const colors = {
      'Entrée': 'bg-green-100 text-green-800',
      'Plat principal': 'bg-blue-100 text-blue-800',
      'Dessert': 'bg-pink-100 text-pink-800',
      'Accompagnement': 'bg-yellow-100 text-yellow-800',
      'Boisson': 'bg-purple-100 text-purple-800',
      'Collation': 'bg-orange-100 text-orange-800',
    };
    return colors[category] || 'bg-gray-100 text-gray-800';
  };

  const getAvailabilityColor = (percentage) => {
    if (percentage >= 100) return 'text-green-600';
    if (percentage >= 75) return 'text-yellow-600';
    if (percentage >= 50) return 'text-orange-600';
    return 'text-red-600';
  };

  if (meals.length === 0) {
    return (
      <div className="bg-white p-6 rounded-lg shadow-md">
        <p className="text-gray-500 text-center">
          Aucun repas ajouté. Utilisez le formulaire ci-dessus pour ajouter votre premier repas.
        </p>
      </div>
    );
  }

  return (
    <div className="bg-white p-6 rounded-lg shadow-md">
      <h2 className="text-2xl font-bold mb-4 text-gray-800">
        Liste des repas ({meals.length})
      </h2>
      
      <div className="space-y-4">
        {meals.map((meal) => {
          const availabilityResult = checkInventoryAvailability(meal, inventory);
          const availabilityPercentage = calculateAvailabilityPercentage(meal, inventory);
          
          return (
            <div key={meal.id} className="border border-gray-200 rounded-lg p-4 hover:shadow-md transition-shadow">
              <div className="flex justify-between items-start mb-3">
                <div className="flex-1">
                  <h3 className="text-xl font-semibold text-gray-800 mb-1">
                    {meal.name}
                  </h3>
                  <div className="flex items-center gap-2 mb-2">
                    <span className={`px-2 py-1 rounded-full text-xs font-medium ${getCategoryColor(meal.category)}`}>
                      {meal.category}
                    </span>
                    <span className={`text-sm font-medium ${getAvailabilityColor(availabilityPercentage)}`}>
                      Disponibilité: {availabilityPercentage}%
                    </span>
                  </div>
                  <p className="text-sm text-gray-500">
                    Créé le: {meal.createdAt}
                  </p>
                </div>
                
                <div className="flex gap-2">
                  <button
                    onClick={() => onEditMeal(meal)}
                    className="px-3 py-1 bg-blue-500 text-white text-sm rounded hover:bg-blue-600 transition-colors"
                  >
                    Modifier
                  </button>
                  <button
                    onClick={() => handleDelete(meal.id)}
                    className="px-3 py-1 bg-red-500 text-white text-sm rounded hover:bg-red-600 transition-colors"
                  >
                    Supprimer
                  </button>
                </div>
              </div>
              
              <div className="mb-3">
                <h4 className="text-sm font-medium text-gray-700 mb-2">
                  Ingrédients ({meal.ingredients.length}):
                </h4>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-2">
                  {meal.ingredients.map((ingredient, index) => {
                    const inventoryItem = inventory.find(item => 
                      item.ingredientName.toLowerCase() === ingredient.name.toLowerCase()
                    );
                    const isAvailable = inventoryItem && 
                      inventoryItem.quantity >= (parseFloat(ingredient.quantity.replace(/[^\d.]/g, '')) || 0);
                    
                    return (
                      <div 
                        key={index} 
                        className={`flex justify-between items-center p-2 rounded text-sm ${
                          isAvailable ? 'bg-green-50 text-green-800' : 'bg-red-50 text-red-800'
                        }`}
                      >
                        <span>{ingredient.name}</span>
                        <span className="font-medium">{ingredient.quantity}</span>
                      </div>
                    );
                  })}
                </div>
              </div>
              
              {!availabilityResult.available && (
                <div className="bg-red-50 border border-red-200 rounded p-3">
                  <h5 className="text-sm font-medium text-red-800 mb-1">
                    Ingrédients manquants:
                  </h5>
                  <ul className="text-sm text-red-700 space-y-1">
                    {availabilityResult.missingIngredients.map((missing, index) => (
                      <li key={index} className="flex items-center">
                        <span className="w-2 h-2 bg-red-400 rounded-full mr-2"></span>
                        {missing}
                      </li>
                    ))}
                  </ul>
                </div>
              )}
            </div>
          );
        })}
      </div>
    </div>
  );
}
