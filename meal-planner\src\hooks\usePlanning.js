import { useSelector, useDispatch } from 'react-redux';
import { 
  getWeeklyPlanning, 
  getDaysOfWeek, 
  getMealTypes,
  setMealForSlot, 
  clearMealFromSlot, 
  clearAllPlanning 
} from '../store/planningSlice';

/**
 * Custom hook for weekly meal planning
 * Provides easy access to planning state and actions
 */
export const usePlanning = () => {
  const dispatch = useDispatch();
  const weeklyPlanning = useSelector((state) => state.planning?.weeklyPlanning || {});
  const loading = useSelector((state) => state.planning?.loading || false);
  const error = useSelector((state) => state.planning?.error || null);

  const daysOfWeek = getDaysOfWeek();
  const mealTypes = getMealTypes();

  const actions = {
    setMeal: (day, mealType, dishId) => 
      dispatch(setMealForSlot({ day, mealType, dishId })),
    clearMeal: (day, mealType) => 
      dispatch(clearMealFromSlot({ day, mealType })),
    clearAllPlanning: () => dispatch(clearAllPlanning()),
  };

  const utils = {
    getMealForSlot: (day, mealType) => weeklyPlanning[day]?.[mealType] || null,
    getPlannedMealsCount: () => {
      let count = 0;
      daysOfWeek.forEach(day => {
        mealTypes.forEach(mealType => {
          if (weeklyPlanning[day]?.[mealType]) count++;
        });
      });
      return count;
    },
    isDishUsedInPlanning: (dishId) => {
      return daysOfWeek.some(day => 
        mealTypes.some(mealType => 
          weeklyPlanning[day]?.[mealType] === dishId
        )
      );
    },
  };

  return {
    weeklyPlanning,
    daysOfWeek,
    mealTypes,
    loading,
    error,
    ...actions,
    ...utils,
  };
};
