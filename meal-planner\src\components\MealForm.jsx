import React, { useEffect, useState } from 'react';
import { useForm, useFieldArray } from 'react-hook-form';
import { yupResolver } from '@hookform/resolvers/yup';
import * as yup from 'yup';
import { useDispatch } from 'react-redux';
import { addMeal, updateMeal, clearMeals } from '../store/mealsSlice';

const schema = yup.object({
  name: yup.string().required('Le nom du repas est requis').max(100, 'Maximum 100 caractères'),
  category: yup.string().required('La catégorie est requise'),
  ingredients: yup.array().of(
    yup.object({
      name: yup.string().required('Le nom de l\'ingrédient est requis'),
      quantity: yup.string().required('La quantité est requise'),
    })
  ).min(1, 'Au moins un ingrédient est requis'),
});

export default function MealForm({ editingMeal, onEditComplete }) {
  const dispatch = useDispatch();
  const [isEditing, setIsEditing] = useState(false);

  const {
    register,
    handleSubmit,
    control,
    reset,
    formState: { errors },
  } = useForm({
    resolver: yupResolver(schema),
    defaultValues: {
      name: '',
      category: 'Matin',
      ingredients: [{ name: '', quantity: '' }],
    },
  });

  const { fields, append, remove } = useFieldArray({
    control,
    name: 'ingredients',
  });

  useEffect(() => {
    if (editingMeal) {
      setIsEditing(true);
      reset({
        name: editingMeal.name,
        category: editingMeal.category,
        ingredients: editingMeal.ingredients,
      });
    }
  }, [editingMeal, reset]);

  const onSubmit = (data) => {
    if (isEditing && editingMeal) {
      dispatch(updateMeal({
        ...editingMeal,
        ...data,
      }));
      setIsEditing(false);
      onEditComplete && onEditComplete();
    } else {
      dispatch(addMeal(data));
    }

    reset({
      name: '',
      category: 'Matin',
      ingredients: [{ name: '', quantity: '' }],
    });
  };

  const handleClear = () => {
    reset({
      name: '',
      category: 'Matin',
      ingredients: [{ name: '', quantity: '' }],
    });
    setIsEditing(false);
    onEditComplete && onEditComplete();
  };

  const handleClearAll = () => {
    if (window.confirm('Êtes-vous sûr de vouloir supprimer tous les repas ?')) {
      dispatch(clearMeals());
    }
  };

  const addIngredient = () => {
    append({ name: '', quantity: '' });
  };

  const categories = [
    'Matin',
    'Midi',
    'Soir'
  ];

  return (
    <div className="bg-white p-6 rounded-lg shadow-md mb-6">
      <h2 className="text-2xl font-bold mb-4 text-gray-800">
        {isEditing ? 'Modifier le repas' : 'Ajouter un nouveau repas'}
      </h2>
      
      <form onSubmit={handleSubmit(onSubmit)} className="space-y-4">
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-1">
            Nom du repas
          </label>
          <input
            {...register('name')}
            type="text"
            className="w-full p-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            placeholder="Ex: Spaghetti Bolognaise"
          />
          {errors.name && (
            <p className="text-red-500 text-sm mt-1">{errors.name.message}</p>
          )}
        </div>

        <div>
          <label className="block text-sm font-medium text-gray-700 mb-1">
            Catégorie
          </label>
          <select
            {...register('category')}
            className="w-full p-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent"
          >
            {categories.map((category) => (
              <option key={category} value={category}>
                {category}
              </option>
            ))}
          </select>
          {errors.category && (
            <p className="text-red-500 text-sm mt-1">{errors.category.message}</p>
          )}
        </div>

        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Ingrédients
          </label>
          {fields.map((field, index) => (
            <div key={field.id} className="flex gap-2 mb-2">
              <input
                {...register(`ingredients.${index}.name`)}
                type="text"
                placeholder="Nom de l'ingrédient"
                className="flex-1 p-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              />
              <input
                {...register(`ingredients.${index}.quantity`)}
                type="text"
                placeholder="Quantité"
                className="w-32 p-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              />
              {fields.length > 1 && (
                <button
                  type="button"
                  onClick={() => remove(index)}
                  className="px-3 py-2 bg-red-500 text-white rounded-md hover:bg-red-600 transition-colors"
                >
                  Supprimer
                </button>
              )}
            </div>
          ))}
          
          {errors.ingredients && (
            <p className="text-red-500 text-sm mt-1">{errors.ingredients.message}</p>
          )}
          
          <button
            type="button"
            onClick={addIngredient}
            className="mt-2 px-4 py-2 bg-green-500 text-white rounded-md hover:bg-green-600 transition-colors"
          >
            Ajouter un ingrédient
          </button>
        </div>

        <div className="flex gap-2 pt-4">
          <button
            type="submit"
            className="px-6 py-2 bg-blue-500 text-white rounded-md hover:bg-blue-600 transition-colors"
          >
            {isEditing ? 'Mettre à jour' : 'Ajouter'}
          </button>
          
          <button
            type="button"
            onClick={handleClear}
            className="px-6 py-2 bg-gray-500 text-white rounded-md hover:bg-gray-600 transition-colors"
          >
            Effacer
          </button>
          
          <button
            type="button"
            onClick={handleClearAll}
            className="px-6 py-2 bg-red-500 text-white rounded-md hover:bg-red-600 transition-colors"
          >
            Supprimer tous
          </button>
        </div>
      </form>
    </div>
  );
}
