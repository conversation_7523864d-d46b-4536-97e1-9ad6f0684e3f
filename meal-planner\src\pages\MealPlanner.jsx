import React, { useState, useEffect } from 'react';
import { useSelector } from 'react-redux';
import { getMeals } from '../store/mealsSlice';
import { getLowStockItems } from '../store/inventorySlice';
import MealForm from '../components/MealForm';
import MealList from '../components/MealList';

export default function MealPlanner() {
  const [editingMeal, setEditingMeal] = useState(null);
  const meals = useSelector((state) => state.meals?.meals || []);
  const lowStockItems = useSelector((state) => state.inventory?.inventory?.filter(item => item.quantity <= item.alertThreshold) || []);

  useEffect(() => {
    // Initialize data from persisted Redux state
    console.log('Planificateur de repas initialisé avec', meals.length, 'repas');
    console.log('Alertes stock faible:', lowStockItems.length);
  }, [meals, lowStockItems]);

  const handleEditMeal = (meal) => {
    setEditingMeal(meal);
  };

  const handleEditComplete = () => {
    setEditingMeal(null);
  };

  return (
    <div className="min-h-screen bg-gray-100">
      {/* Header */}
      <header className="bg-blue-600 text-white p-4 shadow-lg">
        <div className="container mx-auto">
          <h1 className="text-3xl font-bold">Planificateur de Repas</h1>
          <p className="text-blue-100 mt-1">
            Gérez vos repas et votre inventaire facilement
          </p>
        </div>
      </header>

      {/* Main Content */}
      <main className="container mx-auto p-4 space-y-6">
        {/* Low Stock Alert */}
        {lowStockItems.length > 0 && (
          <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
            <h3 className="text-lg font-semibold text-yellow-800 mb-2">
              ⚠️ Alerte Stock Faible
            </h3>
            <p className="text-yellow-700 mb-2">
              Les ingrédients suivants sont en stock faible:
            </p>
            <div className="flex flex-wrap gap-2">
              {lowStockItems.map((item) => (
                <span 
                  key={item.id}
                  className="px-2 py-1 bg-yellow-200 text-yellow-800 rounded text-sm"
                >
                  {item.ingredientName}: {item.quantity}{item.unit} 
                  (seuil: {item.alertThreshold})
                </span>
              ))}
            </div>
          </div>
        )}

        {/* Statistics */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <div className="bg-white p-4 rounded-lg shadow">
            <h3 className="text-lg font-semibold text-gray-700">Total Repas</h3>
            <p className="text-3xl font-bold text-blue-600">{meals.length}</p>
          </div>
          <div className="bg-white p-4 rounded-lg shadow">
            <h3 className="text-lg font-semibold text-gray-700">Alertes Stock</h3>
            <p className="text-3xl font-bold text-yellow-600">{lowStockItems.length}</p>
          </div>
          <div className="bg-white p-4 rounded-lg shadow">
            <h3 className="text-lg font-semibold text-gray-700">Catégories</h3>
            <p className="text-3xl font-bold text-green-600">
              {new Set(meals.map(meal => meal.category)).size}
            </p>
          </div>
        </div>

        {/* Meal Form */}
        <MealForm 
          editingMeal={editingMeal} 
          onEditComplete={handleEditComplete}
        />

        {/* Meal List */}
        <MealList onEditMeal={handleEditMeal} />
      </main>

      {/* Footer */}
      <footer className="bg-gray-800 text-white p-4 mt-8">
        <div className="container mx-auto text-center">
          <p>&copy; 2024 Planificateur de Repas - Application de démonstration React + Redux</p>
        </div>
      </footer>
    </div>
  );
}
