import { createSlice } from "@reduxjs/toolkit";

const mealsSlice = createSlice({
  name: "meals",
  initialState: {
    meals: [
      {
        id: 1,
        name: "Spaghetti Bolognaise",
        category: "Plat principal",
        ingredients: [
          { name: "Spaghetti", quantity: "400g" },
          { name: "Viande hachée", quantity: "300g" },
          { name: "Tomates", quantity: "2 boîtes" },
          { name: "<PERSON>ignon", quantity: "1" }
        ],
        createdAt: (new Date()).toLocaleString()
      },
      {
        id: 2,
        name: "Salade César",
        category: "Entrée",
        ingredients: [
          { name: "<PERSON><PERSON><PERSON>", quantity: "1 tête" },
          { name: "Parmesan", quantity: "100g" },
          { name: "Croûtons", quantity: "50g" },
          { name: "<PERSON>uce César", quantity: "3 cuillères" }
        ],
        createdAt: (new Date()).toLocaleString()
      }
    ],
    loading: false,
    error: null,
  },
  reducers: {
    addMeal: (state, action) => {
      state.meals.push({
        id: state.meals.length + 1,
        ...action.payload,
        createdAt: (new Date()).toLocaleString(),
      });
    },
    updateMeal: (state, action) => {
      const { id, name, category, ingredients } = action.payload;
      const meal = state.meals.find(meal => meal.id === id);
      if (meal) {
        meal.name = name;
        meal.category = category;
        meal.ingredients = ingredients;
      }
    },
    deleteMeal: (state, action) => {
      state.meals = state.meals.filter(meal => meal.id !== action.payload);
    },
    clearMeals: (state) => {
      state.meals = [];
    },
  },
  selectors: {
    getMeals: (state) => state.meals || [],
    getMealById: (state, id) => (state.meals || []).find(meal => meal.id === id),
    getMealsByCategory: (state, category) =>
      (state.meals || []).filter(meal => meal.category === category),
  },
});

export const { getMeals, getMealById, getMealsByCategory } = mealsSlice.selectors;
export const { addMeal, updateMeal, deleteMeal, clearMeals } = mealsSlice.actions;
export default mealsSlice.reducer;
